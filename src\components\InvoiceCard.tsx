import { Eye, Download, Calendar, DollarSign } from 'lucide-react';
import { InvoiceCardProps } from '@/types';
import { invoiceService } from '@/lib/services/invoiceService';

export default function InvoiceCard({ invoice, onView }: InvoiceCardProps) {
  const handleDownload = async () => {
    try {
      const blob = await invoiceService.downloadInvoice(invoice.invoice_file_name);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = invoice.invoice_file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download invoice');
    }
  };

  const statusColor = invoiceService.getStatusColor(invoice.status);
  const isOverdue = invoiceService.isOverdue(invoice.due_date);
  const daysUntilDue = invoiceService.calculateDaysUntilDue(invoice.due_date);

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {invoice.invoice_number}
              </h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}`}>
                {invoice.status}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-2">
              Client: <span className="font-medium">{invoice.client_name}</span>
            </p>
            
            <div className="flex items-center text-sm text-gray-500 mb-2">
              <DollarSign className="h-4 w-4 mr-1" />
              <span className="font-medium text-gray-900">
                {invoiceService.formatCurrency(invoice.amount)}
              </span>
            </div>
            
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              <span>Due: {invoiceService.formatDate(invoice.due_date)}</span>
              {isOverdue && (
                <span className="ml-2 text-red-600 font-medium">
                  (Overdue)
                </span>
              )}
              {!isOverdue && daysUntilDue <= 7 && daysUntilDue > 0 && (
                <span className="ml-2 text-yellow-600 font-medium">
                  ({daysUntilDue} days left)
                </span>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-4 flex space-x-2">
          <button
            onClick={() => onView?.(invoice.id)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Eye className="h-4 w-4 mr-1" />
            View Details
          </button>
          
          <button
            onClick={handleDownload}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Download className="h-4 w-4 mr-1" />
            Download PDF
          </button>
        </div>
      </div>
    </div>
  );
}
