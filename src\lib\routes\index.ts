/**
 * Centralized Route Management System
 * 
 * This module provides a unified way to manage all API routes in the application.
 * All routes are defined in the registry and processed through a single handler.
 */

// Core types
export type {
  HttpMethod,
  RouteHandler,
  RouteContext,
  RouteDefinition,
  RouteRegistry,
  RouteMatch,
  ApiResponse,
  RouteMiddleware,
  RouteConfig,
} from './types';

// Route registry
export {
  routeRegistry,
  getRoutesByTag,
  getAllRoutes,
  getRoute,
  registerRoute,
  getRoutePaths,
} from './registry';

// Route matcher
export { RouteMatcher } from './matcher';

// Route processor
export { RouteProcessor, defaultRouteProcessor } from './processor';

// Route handlers
export { authHandlers } from './handlers/auth';
export { invoiceHandlers } from './handlers/invoice';
export { mediaHandlers } from './handlers/media';
export { csrfHandlers } from './handlers/csrf';

/**
 * Utility functions for working with routes
 */

/**
 * Get all available API endpoints
 */
export function getApiEndpoints() {
  return getRoutePaths().map(({ method, path }) => ({
    method,
    path: `/api${path}`,
    fullPath: `${method} /api${path}`,
  }));
}

/**
 * Check if an API endpoint exists
 */
export function apiEndpointExists(method: string, path: string): boolean {
  const normalizedPath = path.replace('/api', '');
  return getRoute(method, normalizedPath) !== undefined;
}

/**
 * Get route information for documentation
 */
export function getRouteDocumentation() {
  return getAllRoutes().map(route => ({
    method: route.method,
    path: `/api${route.path}`,
    description: route.description,
    tags: route.tags,
    requiresAuth: route.requiresAuth,
    rateLimit: route.rateLimit,
  }));
}

/**
 * Register a new API route
 */
export function addApiRoute(
  method: string,
  path: string,
  handler: RouteHandler,
  options: Partial<RouteDefinition> = {}
) {
  const key = `${method.toUpperCase()}:${path}`;
  registerRoute(key, {
    path,
    method: method.toUpperCase() as any,
    handler,
    ...options,
  });
}
