import { getAllRoutes, getRoutesByTag, routeRegistry } from './registry';
import { RouteMatcher } from './matcher';
import { RouteDefinition } from './types';

/**
 * Route administration utilities
 * Provides tools for managing and inspecting the route system
 */
export class RouteAdmin {
  /**
   * Get comprehensive route statistics
   */
  static getRouteStats() {
    const routes = getAllRoutes();
    const methodCounts = routes.reduce((acc, route) => {
      acc[route.method] = (acc[route.method] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const tagCounts = routes.reduce((acc, route) => {
      route.tags?.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const authRoutes = routes.filter(r => r.requiresAuth).length;
    const rateLimitedRoutes = routes.filter(r => r.rateLimit).length;

    return {
      totalRoutes: routes.length,
      methodCounts,
      tagCounts,
      authRoutes,
      rateLimitedRoutes,
      publicRoutes: routes.length - authRoutes,
    };
  }

  /**
   * Get all routes in a formatted table structure
   */
  static getRouteTable() {
    const routes = getAllRoutes();
    return routes.map(route => ({
      method: route.method,
      path: route.path,
      auth: route.requiresAuth ? '🔒' : '🔓',
      rateLimit: route.rateLimit ? '⏱️' : '➖',
      description: route.description || 'No description',
      tags: route.tags?.join(', ') || 'None',
    }));
  }

  /**
   * Find routes by pattern
   */
  static findRoutes(pattern: string): RouteDefinition[] {
    const routes = getAllRoutes();
    const regex = new RegExp(pattern, 'i');
    
    return routes.filter(route => 
      regex.test(route.path) || 
      regex.test(route.description || '') ||
      route.tags?.some(tag => regex.test(tag))
    );
  }

  /**
   * Get routes grouped by tags
   */
  static getRoutesByTags() {
    const routes = getAllRoutes();
    const grouped: Record<string, RouteDefinition[]> = {};

    routes.forEach(route => {
      if (route.tags && route.tags.length > 0) {
        route.tags.forEach(tag => {
          if (!grouped[tag]) {
            grouped[tag] = [];
          }
          grouped[tag].push(route);
        });
      } else {
        if (!grouped['untagged']) {
          grouped['untagged'] = [];
        }
        grouped['untagged'].push(route);
      }
    });

    return grouped;
  }

  /**
   * Validate route registry for common issues
   */
  static validateRoutes() {
    const routes = getAllRoutes();
    const issues: string[] = [];
    const pathMethodCombos = new Set<string>();

    routes.forEach(route => {
      const combo = `${route.method}:${route.path}`;
      
      // Check for duplicates
      if (pathMethodCombos.has(combo)) {
        issues.push(`Duplicate route: ${combo}`);
      }
      pathMethodCombos.add(combo);

      // Check for missing descriptions
      if (!route.description) {
        issues.push(`Missing description: ${combo}`);
      }

      // Check for missing tags
      if (!route.tags || route.tags.length === 0) {
        issues.push(`Missing tags: ${combo}`);
      }

      // Check for auth routes without rate limiting
      if (route.requiresAuth && !route.rateLimit) {
        issues.push(`Auth route without rate limiting: ${combo}`);
      }

      // Check for invalid path patterns
      if (!route.path.startsWith('/')) {
        issues.push(`Invalid path (should start with /): ${combo}`);
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
      totalRoutes: routes.length,
    };
  }

  /**
   * Get route performance metrics (mock data for demonstration)
   */
  static getRouteMetrics() {
    const routes = getAllRoutes();
    
    return routes.map(route => ({
      method: route.method,
      path: route.path,
      // Mock metrics - in a real app, these would come from monitoring
      avgResponseTime: Math.floor(Math.random() * 500) + 50,
      requestCount: Math.floor(Math.random() * 10000),
      errorRate: Math.random() * 0.05, // 0-5% error rate
      lastAccessed: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    }));
  }

  /**
   * Generate OpenAPI/Swagger documentation structure
   */
  static generateOpenApiSpec() {
    const routes = getAllRoutes();
    const paths: Record<string, any> = {};

    routes.forEach(route => {
      if (!paths[route.path]) {
        paths[route.path] = {};
      }

      paths[route.path][route.method.toLowerCase()] = {
        summary: route.description,
        tags: route.tags,
        security: route.requiresAuth ? [{ bearerAuth: [] }] : [],
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'number' },
                    message: { type: 'string' },
                    data: { type: 'object' },
                  },
                },
              },
            },
          },
          400: { description: 'Bad Request' },
          401: { description: 'Unauthorized' },
          404: { description: 'Not Found' },
          429: { description: 'Too Many Requests' },
          500: { description: 'Internal Server Error' },
        },
      };

      // Add rate limiting info to description
      if (route.rateLimit) {
        paths[route.path][route.method.toLowerCase()].description = 
          `${route.description} (Rate limited: ${route.rateLimit.requests} requests per ${route.rateLimit.windowMs}ms)`;
      }
    });

    return {
      openapi: '3.0.0',
      info: {
        title: 'Whitelotus Assignment API',
        version: '1.0.0',
        description: 'Centralized API with unified route management',
      },
      paths,
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
    };
  }

  /**
   * Test route matching
   */
  static testRouteMatching(method: string, path: string) {
    const match = RouteMatcher.match(method, path);
    
    return {
      matched: !!match,
      route: match?.definition,
      params: match?.params || {},
      suggestions: match ? [] : this.getSimilarRoutes(path),
    };
  }

  /**
   * Get similar routes for suggestions
   */
  private static getSimilarRoutes(path: string): string[] {
    const routes = getAllRoutes();
    const pathParts = path.split('/').filter(Boolean);
    
    return routes
      .map(route => {
        const routeParts = route.path.split('/').filter(Boolean);
        let similarity = 0;
        
        for (let i = 0; i < Math.min(pathParts.length, routeParts.length); i++) {
          if (pathParts[i] === routeParts[i] || routeParts[i].startsWith(':')) {
            similarity++;
          }
        }
        
        return {
          path: route.path,
          method: route.method,
          similarity: similarity / Math.max(pathParts.length, routeParts.length),
        };
      })
      .filter(item => item.similarity > 0.3)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 5)
      .map(item => `${item.method} ${item.path}`);
  }
}
