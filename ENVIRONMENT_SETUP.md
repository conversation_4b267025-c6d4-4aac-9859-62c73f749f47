# Environment Configuration Guide

This document explains how to set up environment variables for the WhiteLotus Invoice Application.

## Quick Start

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env.local
   ```

2. **Edit the `.env.local` file** with your actual values (see sections below for details)

3. **Start the development server:**
   ```bash
   npm run dev
   ```

## Environment Files

### File Types

- **`.env`** - Default environment variables (committed to repo)
- **`.env.local`** - Local overrides (not committed, use for development)
- **`.env.example`** - Template file (committed to repo)
- **`.env.production`** - Production-specific variables (not committed)

### Loading Order

Next.js loads environment variables in this order (later files override earlier ones):

1. `.env`
2. `.env.local`
3. `.env.development` (when NODE_ENV=development)
4. `.env.development.local` (when NODE_ENV=development)

## Required Environment Variables

### Authentication & Security

```bash
# JWT Secret - CRITICAL: Change this in production!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-please

# Demo credentials (replace with proper auth system in production)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Pass@1234
```

### API Configuration

```bash
# Public API URL (exposed to client)
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# API settings
API_TIMEOUT=10000
API_RETRIES=3
```

## Optional Environment Variables

### Application Settings

```bash
# Cookie configuration
COOKIE_NAME=auth-token
COOKIE_MAX_AGE=604800

# Pagination
PAGINATION_DEFAULT_LIMIT=5
PAGINATION_MAX_LIMIT=100

# ISR settings
ISR_REVALIDATE=60
```

### Security Settings

```bash
# Rate limiting
RATE_LIMIT_LOGIN_ATTEMPTS=5
RATE_LIMIT_LOGIN_WINDOW=60000
RATE_LIMIT_DOWNLOAD_ATTEMPTS=20
RATE_LIMIT_DOWNLOAD_WINDOW=60000
```

### Development Settings

```bash
# Development configuration
DEBUG_LOGGING=true
PORT=3000
NODE_ENV=development
```

## Production Setup

### 1. Generate Secure JWT Secret

```bash
# Generate a secure random secret
openssl rand -base64 32
```

### 2. Set Production Environment Variables

Create a `.env.production` file or set variables in your hosting platform:

```bash
NODE_ENV=production
JWT_SECRET=your-generated-secure-secret-here
NEXT_PUBLIC_API_URL=https://your-production-api.com/api
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-production-password
```

### 3. Security Checklist

- [ ] Changed default JWT_SECRET
- [ ] Updated admin credentials
- [ ] Set production API URL
- [ ] Configured rate limiting appropriately
- [ ] Enabled HTTPS in production
- [ ] Set secure cookie settings

## Environment Variable Types

### Public Variables (NEXT_PUBLIC_*)

These are exposed to the browser and should not contain secrets:

- `NEXT_PUBLIC_API_URL` - API base URL

### Private Variables

These are only available on the server side:

- `JWT_SECRET` - JWT signing secret
- `ADMIN_EMAIL` - Admin email
- `ADMIN_PASSWORD` - Admin password

## Common Issues

### 1. Environment Variables Not Loading

**Problem:** Variables are undefined in your code.

**Solutions:**
- Restart the development server after changing `.env` files
- Check variable names for typos
- Ensure `.env.local` exists and has correct values
- For client-side variables, ensure they start with `NEXT_PUBLIC_`

### 2. JWT Secret Warnings

**Problem:** Seeing warnings about default JWT secret.

**Solution:** Set a proper `JWT_SECRET` in your environment file:
```bash
JWT_SECRET=your-secure-secret-here
```

### 3. API Connection Issues

**Problem:** Cannot connect to API.

**Solutions:**
- Check `NEXT_PUBLIC_API_URL` is correct
- Ensure API server is running
- Verify network connectivity

## Best Practices

### 1. Never Commit Secrets

- Add all `.env*` files (except `.env.example`) to `.gitignore`
- Use different secrets for different environments
- Rotate secrets regularly

### 2. Use Strong Secrets

```bash
# Good: Generate random secrets
JWT_SECRET=$(openssl rand -base64 32)

# Bad: Predictable or default secrets
JWT_SECRET=secret123
```

### 3. Environment-Specific Configuration

```bash
# Development
NODE_ENV=development
DEBUG_LOGGING=true
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Production
NODE_ENV=production
DEBUG_LOGGING=false
NEXT_PUBLIC_API_URL=https://api.yourcompany.com
```

### 4. Documentation

- Keep this file updated when adding new variables
- Document the purpose of each variable
- Provide example values in `.env.example`

## Deployment Platforms

### Vercel

Set environment variables in the Vercel dashboard:
1. Go to your project settings
2. Navigate to "Environment Variables"
3. Add each variable with appropriate environment scope

### Netlify

Set environment variables in the Netlify dashboard:
1. Go to Site settings
2. Navigate to "Environment variables"
3. Add each variable

### Docker

Use environment files or pass variables directly:

```dockerfile
# Using environment file
ENV_FILE=.env.production

# Or pass directly
ENV JWT_SECRET=your-secret-here
ENV NEXT_PUBLIC_API_URL=https://api.example.com
```

## Support

If you encounter issues with environment configuration:

1. Check this documentation
2. Verify your `.env.local` file exists and has correct syntax
3. Restart your development server
4. Check the browser console and server logs for error messages

For additional help, refer to the [Next.js Environment Variables documentation](https://nextjs.org/docs/basic-features/environment-variables).
