import { RouteDefinition, RouteMatch } from './types';
import { routeRegistry } from './registry';

/**
 * Route matcher utility for matching incoming requests to route definitions
 */
export class RouteMatcher {
  /**
   * Match a request path and method to a route definition
   */
  static match(method: string, pathname: string): RouteMatch | null {
    const normalizedMethod = method.toUpperCase();
    
    // First try exact match
    const exactKey = `${normalizedMethod}:${pathname}`;
    if (routeRegistry[exactKey]) {
      return {
        definition: routeRegistry[exactKey],
        params: {},
      };
    }

    // Try pattern matching for dynamic routes
    for (const [key, definition] of Object.entries(routeRegistry)) {
      const [routeMethod, routePath] = key.split(':');
      
      if (routeMethod !== normalizedMethod) {
        continue;
      }

      const match = this.matchPattern(routePath, pathname);
      if (match) {
        return {
          definition,
          params: match.params,
        };
      }
    }

    return null;
  }

  /**
   * Match a URL pattern with parameters (e.g., /auth/invoice/:id)
   */
  private static matchPattern(pattern: string, pathname: string): { params: Record<string, string> } | null {
    const patternParts = pattern.split('/').filter(Boolean);
    const pathnameParts = pathname.split('/').filter(Boolean);

    if (patternParts.length !== pathnameParts.length) {
      return null;
    }

    const params: Record<string, string> = {};

    for (let i = 0; i < patternParts.length; i++) {
      const patternPart = patternParts[i];
      const pathnamePart = pathnameParts[i];

      if (patternPart.startsWith(':')) {
        // Dynamic parameter
        const paramName = patternPart.slice(1);
        params[paramName] = decodeURIComponent(pathnamePart);
      } else if (patternPart !== pathnamePart) {
        // Static part doesn't match
        return null;
      }
    }

    return { params };
  }

  /**
   * Get all routes that match a specific pattern
   */
  static getMatchingRoutes(pattern: string): RouteDefinition[] {
    return Object.values(routeRegistry).filter(route => {
      return this.matchPattern(route.path, pattern) !== null;
    });
  }

  /**
   * Check if a route exists
   */
  static routeExists(method: string, pathname: string): boolean {
    return this.match(method, pathname) !== null;
  }

  /**
   * Get route definition by method and path
   */
  static getRouteDefinition(method: string, pathname: string): RouteDefinition | null {
    const match = this.match(method, pathname);
    return match ? match.definition : null;
  }

  /**
   * Extract parameters from a matched route
   */
  static extractParams(pattern: string, pathname: string): Record<string, string> {
    const match = this.matchPattern(pattern, pathname);
    return match ? match.params : {};
  }

  /**
   * Build a URL from a pattern and parameters
   */
  static buildUrl(pattern: string, params: Record<string, string> = {}): string {
    let url = pattern;
    
    for (const [key, value] of Object.entries(params)) {
      url = url.replace(`:${key}`, encodeURIComponent(value));
    }
    
    return url;
  }

  /**
   * Validate that all required parameters are provided
   */
  static validateParams(pattern: string, params: Record<string, string>): boolean {
    const requiredParams = pattern.match(/:(\w+)/g);
    
    if (!requiredParams) {
      return true; // No parameters required
    }

    return requiredParams.every(param => {
      const paramName = param.slice(1); // Remove the ':'
      return params[paramName] !== undefined;
    });
  }
}
