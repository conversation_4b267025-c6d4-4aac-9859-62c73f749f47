# Route System Examples

This document provides practical examples of how to use the centralized route management system.

## Example 1: Simple GET Route

Create a simple endpoint that returns system status:

```typescript
// /src/lib/routes/handlers/system.ts
import { NextRequest, NextResponse } from 'next/server';
import { getSecurityHeaders } from '@/lib/security';

export const systemHandlers = {
  async getStatus(request: NextRequest): Promise<NextResponse> {
    return NextResponse.json({
      status: 200,
      message: 'System is healthy',
      data: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV,
      },
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  },
};
```

Add to registry:

```typescript
// /src/lib/routes/registry.ts
import { systemHandlers } from './handlers/system';

export const routeRegistry: RouteRegistry = {
  // ... existing routes
  
  'GET:/system/status': {
    path: '/system/status',
    method: 'GET',
    handler: systemHandlers.getStatus,
    requiresAuth: false,
    description: 'Get system status',
    tags: ['system'],
  },
};
```

## Example 2: Authenticated Route with Parameters

Create an endpoint to get user profile by ID:

```typescript
// /src/lib/routes/handlers/users.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUserFromRequest } from '@/lib/auth';
import { getSecurityHeaders } from '@/lib/security';
import { RouteContext } from '../types';

export const userHandlers = {
  async getProfile(request: NextRequest, context?: RouteContext): Promise<NextResponse> {
    try {
      // Authentication is handled automatically by the system
      const currentUser = await getCurrentUserFromRequest(request);
      const userId = context?.params?.id;

      if (!userId) {
        return NextResponse.json(
          { status: 400, message: 'User ID is required' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(userId)) {
        return NextResponse.json(
          { status: 400, message: 'Invalid user ID format' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Check if user can access this profile (example: only own profile or admin)
      if (currentUser?.id !== userId && currentUser?.role !== 'admin') {
        return NextResponse.json(
          { status: 403, message: 'Access denied' },
          { status: 403, headers: getSecurityHeaders() }
        );
      }

      // Mock user data (in real app, fetch from database)
      const userProfile = {
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        createdAt: '2023-01-01T00:00:00.000Z',
      };

      return NextResponse.json({
        status: 200,
        message: 'User profile retrieved successfully',
        data: userProfile,
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });

    } catch (error) {
      console.error('Get user profile error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },
};
```

Add to registry:

```typescript
'GET:/users/:id': {
  path: '/users/:id',
  method: 'GET',
  handler: userHandlers.getProfile,
  requiresAuth: true,
  rateLimit: {
    requests: 30,
    windowMs: 60000, // 1 minute
  },
  description: 'Get user profile by ID',
  tags: ['users'],
},
```

## Example 3: POST Route with Validation

Create an endpoint to update user settings:

```typescript
// /src/lib/routes/handlers/users.ts (continued)
export const userHandlers = {
  // ... previous handlers

  async updateSettings(request: NextRequest, context?: RouteContext): Promise<NextResponse> {
    try {
      const currentUser = await getCurrentUser();
      
      if (!currentUser) {
        return NextResponse.json(
          { status: 401, message: 'Unauthorized' },
          { status: 401, headers: getSecurityHeaders() }
        );
      }

      const body = await request.json();
      const { theme, notifications, language } = body;

      // Validate input
      const validThemes = ['light', 'dark', 'auto'];
      const validLanguages = ['en', 'es', 'fr', 'de'];

      if (theme && !validThemes.includes(theme)) {
        return NextResponse.json(
          { status: 400, message: 'Invalid theme value' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      if (language && !validLanguages.includes(language)) {
        return NextResponse.json(
          { status: 400, message: 'Invalid language value' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      if (notifications !== undefined && typeof notifications !== 'boolean') {
        return NextResponse.json(
          { status: 400, message: 'Notifications must be a boolean' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Update settings (mock implementation)
      const updatedSettings = {
        userId: currentUser.id,
        theme: theme || 'light',
        notifications: notifications !== undefined ? notifications : true,
        language: language || 'en',
        updatedAt: new Date().toISOString(),
      };

      return NextResponse.json({
        status: 200,
        message: 'Settings updated successfully',
        data: updatedSettings,
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });

    } catch (error) {
      console.error('Update settings error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },
};
```

Add to registry:

```typescript
'POST:/users/settings': {
  path: '/users/settings',
  method: 'POST',
  handler: userHandlers.updateSettings,
  requiresAuth: true,
  rateLimit: {
    requests: 10,
    windowMs: 60000, // 1 minute
  },
  description: 'Update user settings',
  tags: ['users'],
},
```

## Example 4: File Upload Route

Create an endpoint for file uploads:

```typescript
// /src/lib/routes/handlers/files.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { getSecurityHeaders } from '@/lib/security';

export const fileHandlers = {
  async uploadFile(request: NextRequest): Promise<NextResponse> {
    try {
      const currentUser = await getCurrentUser();
      
      if (!currentUser) {
        return NextResponse.json(
          { status: 401, message: 'Unauthorized' },
          { status: 401, headers: getSecurityHeaders() }
        );
      }

      const formData = await request.formData();
      const file = formData.get('file') as File;

      if (!file) {
        return NextResponse.json(
          { status: 400, message: 'No file provided' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json(
          { status: 400, message: 'Invalid file type' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        return NextResponse.json(
          { status: 400, message: 'File too large' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Mock file processing (in real app, save to storage)
      const fileInfo = {
        id: `file_${Date.now()}`,
        name: file.name,
        type: file.type,
        size: file.size,
        uploadedBy: currentUser.id,
        uploadedAt: new Date().toISOString(),
        url: `/files/${file.name}`, // Mock URL
      };

      return NextResponse.json({
        status: 200,
        message: 'File uploaded successfully',
        data: fileInfo,
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });

    } catch (error) {
      console.error('File upload error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },
};
```

Add to registry:

```typescript
'POST:/files/upload': {
  path: '/files/upload',
  method: 'POST',
  handler: fileHandlers.uploadFile,
  requiresAuth: true,
  rateLimit: {
    requests: 5,
    windowMs: 60000, // 1 minute
  },
  description: 'Upload a file',
  tags: ['files'],
},
```

## Example 5: Custom Middleware

Create a route with custom middleware:

```typescript
// /src/lib/routes/middleware/admin.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { getSecurityHeaders } from '@/lib/security';
import { RouteMiddleware } from '../types';

export const adminMiddleware: RouteMiddleware = async (request, context, next) => {
  const user = await getCurrentUser();
  
  if (!user || user.role !== 'admin') {
    return NextResponse.json(
      { status: 403, message: 'Admin access required' },
      { status: 403, headers: getSecurityHeaders() }
    );
  }

  // Add admin flag to context
  context.isAdmin = true;
  
  return await next();
};

// Usage in route processor
import { defaultRouteProcessor } from '@/lib/routes/processor';
defaultRouteProcessor.addGlobalMiddleware(adminMiddleware);
```

## Example 6: Testing Routes

Test your routes:

```typescript
// /src/lib/routes/__tests__/users.test.ts
import { NextRequest } from 'next/server';
import { userHandlers } from '../handlers/users';

describe('User Handlers', () => {
  it('should get user profile', async () => {
    const request = new NextRequest('http://localhost:3000/api/users/123');
    const context = { params: { id: '123' } };
    
    const response = await userHandlers.getProfile(request, context);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.message).toBe('User profile retrieved successfully');
  });
});
```

## Usage Examples

### Making Requests

```javascript
// GET request
const response = await fetch('/api/system/status');
const data = await response.json();

// POST request with authentication
const response = await fetch('/api/users/settings', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  },
  body: JSON.stringify({
    theme: 'dark',
    notifications: true,
    language: 'en',
  }),
});

// File upload
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/files/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
  body: formData,
});
```

These examples demonstrate the key patterns for creating routes in the centralized system. Each route follows consistent patterns for error handling, authentication, validation, and response formatting.
