'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, DollarSign, User, Calendar, FileText } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { invoiceService } from '@/lib/services/invoiceService';
import { CreateInvoiceRequest, FormErrors } from '@/types';
import { ROUTES } from '@/lib/config';
import { validateClientName, validateAmount, validateDate, sanitizeInput } from '@/lib/security';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function CreateInvoicePage() {
  const [formData, setFormData] = useState<CreateInvoiceRequest>({
    client_name: '',
    amount: 0,
    status: 'PENDING',
    due_date: '',
    invoice_file_name: '',
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  if (!isAuthenticated) {
    router.push(ROUTES.LOGIN);
    return null;
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Client name validation
    if (!formData.client_name.trim()) {
      newErrors.client_name = 'Client name is required';
    } else if (!validateClientName(formData.client_name.trim())) {
      newErrors.client_name = 'Client name contains invalid characters';
    }

    // Amount validation
    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0';
    } else if (!validateAmount(formData.amount)) {
      newErrors.amount = 'Amount must be between $0.01 and $999,999.99';
    }

    // Due date validation
    if (!formData.due_date) {
      newErrors.due_date = 'Due date is required';
    } else if (!validateDate(formData.due_date)) {
      newErrors.due_date = 'Due date must be in the future';
    }

    // File name validation
    if (!formData.invoice_file_name.trim()) {
      newErrors.invoice_file_name = 'Invoice file name is required';
    } else if (!/^[a-zA-Z0-9_-]+\.pdf$/.test(formData.invoice_file_name.trim())) {
      newErrors.invoice_file_name = 'File name must be a valid PDF filename (e.g., invoice_001.pdf)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof CreateInvoiceRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'client_name' || field === 'invoice_file_name' 
        ? sanitizeInput(value as string)
        : value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const response = await invoiceService.createInvoice(formData);
      
      if (response.status === 200 && response.data) {
        // Redirect to the new invoice details page
        router.push(`${ROUTES.INVOICE_DETAILS}/${response.data.id}`);
      } else {
        throw new Error(response.message || 'Failed to create invoice');
      }
    } catch (error) {
      console.error('Failed to create invoice:', error);
      setSubmitError(error instanceof Error ? error.message : 'Failed to create invoice');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push(ROUTES.DASHBOARD);
  };

  // Generate suggested filename based on client name
  const generateFileName = () => {
    if (formData.client_name.trim()) {
      const sanitizedName = formData.client_name
        .trim()
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .substring(0, 20);
      const timestamp = Date.now();
      return `invoice_${sanitizedName}_${timestamp}.pdf`;
    }
    return '';
  };

  const fillSuggestedFileName = () => {
    const suggested = generateFileName();
    if (suggested) {
      handleInputChange('invoice_file_name', suggested);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={handleBack}
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Dashboard
            </button>
            
            <h1 className="text-3xl font-bold text-gray-900">
              Create New Invoice
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Fill in the details to create a new invoice
            </p>
          </div>

          {/* Form */}
          <div className="bg-white shadow sm:rounded-lg">
            <form onSubmit={handleSubmit} className="space-y-6 p-6">
              {submitError && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="text-sm text-red-700">{submitError}</div>
                </div>
              )}

              {/* Client Name */}
              <div>
                <label htmlFor="client_name" className="block text-sm font-medium text-gray-700">
                  <User className="inline h-4 w-4 mr-1" />
                  Client Name
                </label>
                <input
                  type="text"
                  id="client_name"
                  value={formData.client_name}
                  onChange={(e) => handleInputChange('client_name', e.target.value)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                    errors.client_name ? 'border-red-300' : ''
                  }`}
                  placeholder="Enter client name"
                />
                {errors.client_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.client_name}</p>
                )}
              </div>

              {/* Amount */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                  <DollarSign className="inline h-4 w-4 mr-1" />
                  Amount
                </label>
                <input
                  type="number"
                  id="amount"
                  step="0.01"
                  min="0.01"
                  max="999999.99"
                  value={formData.amount || ''}
                  onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                    errors.amount ? 'border-red-300' : ''
                  }`}
                  placeholder="0.00"
                />
                {errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
                )}
              </div>

              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value as any)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="PENDING">Pending</option>
                  <option value="PAID">Paid</option>
                  <option value="OVERDUE">Overdue</option>
                </select>
              </div>

              {/* Due Date */}
              <div>
                <label htmlFor="due_date" className="block text-sm font-medium text-gray-700">
                  <Calendar className="inline h-4 w-4 mr-1" />
                  Due Date
                </label>
                <input
                  type="date"
                  id="due_date"
                  value={formData.due_date}
                  onChange={(e) => handleInputChange('due_date', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                    errors.due_date ? 'border-red-300' : ''
                  }`}
                />
                {errors.due_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.due_date}</p>
                )}
              </div>

              {/* Invoice File Name */}
              <div>
                <label htmlFor="invoice_file_name" className="block text-sm font-medium text-gray-700">
                  <FileText className="inline h-4 w-4 mr-1" />
                  Invoice File Name
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <input
                    type="text"
                    id="invoice_file_name"
                    value={formData.invoice_file_name}
                    onChange={(e) => handleInputChange('invoice_file_name', e.target.value)}
                    className={`flex-1 block w-full border-gray-300 rounded-l-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                      errors.invoice_file_name ? 'border-red-300' : ''
                    }`}
                    placeholder="invoice_001.pdf"
                  />
                  <button
                    type="button"
                    onClick={fillSuggestedFileName}
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
                  >
                    Generate
                  </button>
                </div>
                {errors.invoice_file_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.invoice_file_name}</p>
                )}
                <p className="mt-1 text-sm text-gray-500">
                  Must be a valid PDF filename (e.g., invoice_001.pdf)
                </p>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-6">
                <button
                  type="button"
                  onClick={handleBack}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Create Invoice
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
