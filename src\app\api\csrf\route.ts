import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUserFromRequest } from '@/lib/auth';
import { generateCSRFToken, getSecurityHeaders } from '@/lib/security';
import { csrfHandlers } from '@/lib/routes/handlers/csrf';
import { createCompatibleHandler } from '@/lib/routes/compatibility';

// Use the centralized handler
export const GET = createCompatibleHandler(csrfHandlers.getToken);

// Legacy handler for backward compatibility (will be removed in future)
export async function GET_LEGACY(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Generate CSRF token
    const token = generateCSRFToken();

    return NextResponse.json({
      status: 200,
      message: 'CSRF token generated',
      token,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
