# Centralized Route Management System

This document describes the new centralized route management system that provides a unified way to handle all API routes in the application.

## Overview

The centralized route system consolidates all API routes into a single management structure, making it easier to:

- **Manage routes**: All routes are defined in one place
- **Apply middleware**: Global middleware can be applied to all routes
- **Handle authentication**: Centralized authentication checking
- **Rate limiting**: Consistent rate limiting across all endpoints
- **Documentation**: Auto-generated API documentation
- **Testing**: Easier route testing and validation

## Architecture

### Core Components

1. **Route Registry** (`/src/lib/routes/registry.ts`)
   - Central registry containing all API route definitions
   - Single source of truth for all endpoints

2. **Route Processor** (`/src/lib/routes/processor.ts`)
   - Main processor that handles all incoming requests
   - Applies middleware, authentication, and rate limiting

3. **Route Matcher** (`/src/lib/routes/matcher.ts`)
   - Matches incoming requests to route definitions
   - Handles dynamic parameters (e.g., `/invoice/:id`)

4. **Route Handlers** (`/src/lib/routes/handlers/`)
   - Individual handler functions for each route
   - Organized by feature (auth, invoice, media, etc.)

5. **Unified Entry Point** (`/src/app/api/[[...route]]/route.ts`)
   - Single Next.js route that handles all API requests
   - Delegates to the route processor

## Current Routes

### Authentication Routes
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout  
- `GET /auth/me` - Get current user information

### Invoice Routes
- `GET /auth/invoice` - List invoices with pagination
- `POST /auth/invoice` - Create a new invoice
- `GET /auth/invoice/:id` - Get invoice details by ID

### Media Routes
- `GET /invoice/:filename` - Download invoice file

### Security Routes
- `GET /csrf` - Get CSRF token

## Adding New Routes

### Method 1: Using the Route Registry

```typescript
import { registerRoute } from '@/lib/routes/registry';
import { RouteHandler } from '@/lib/routes/types';

const myHandler: RouteHandler = async (request, context) => {
  // Your route logic here
  return NextResponse.json({ message: 'Success' });
};

// Register the route
registerRoute('GET:/my-endpoint', {
  path: '/my-endpoint',
  method: 'GET',
  handler: myHandler,
  requiresAuth: true,
  rateLimit: {
    requests: 10,
    windowMs: 60000, // 1 minute
  },
  description: 'My custom endpoint',
  tags: ['custom'],
});
```

### Method 2: Using the Helper Function

```typescript
import { addApiRoute } from '@/lib/routes';

addApiRoute('GET', '/my-endpoint', myHandler, {
  requiresAuth: true,
  rateLimit: { requests: 10, windowMs: 60000 },
  description: 'My custom endpoint',
  tags: ['custom'],
});
```

### Method 3: Adding to Handler Files

1. Create or update a handler file in `/src/lib/routes/handlers/`
2. Add your handler function
3. Update the registry to include the new route

```typescript
// /src/lib/routes/handlers/my-feature.ts
export const myFeatureHandlers = {
  async myEndpoint(request: NextRequest, context?: RouteContext): Promise<NextResponse> {
    // Your logic here
    return NextResponse.json({ message: 'Success' });
  },
};

// Update registry.ts
'GET:/my-endpoint': {
  path: '/my-endpoint',
  method: 'GET',
  handler: myFeatureHandlers.myEndpoint,
  requiresAuth: true,
  description: 'My custom endpoint',
  tags: ['my-feature'],
},
```

## Route Configuration Options

### Basic Route Definition

```typescript
interface RouteDefinition {
  path: string;                    // Route path pattern
  method: HttpMethod;              // HTTP method
  handler: RouteHandler;           // Handler function
  requiresAuth?: boolean;          // Authentication required
  rateLimit?: RateLimitConfig;     // Rate limiting config
  description?: string;            // Route description
  tags?: string[];                 // Tags for grouping
}
```

### Rate Limiting

```typescript
rateLimit: {
  requests: 10,                    // Number of requests
  windowMs: 60000,                 // Time window in milliseconds
  keyGenerator?: (request) => string // Custom key generator
}
```

### Authentication

Set `requiresAuth: true` to require authentication. The system will:
- Check for valid JWT token
- Add user object to route context
- Return 401 if not authenticated

## Dynamic Routes

Use `:parameter` syntax for dynamic routes:

```typescript
'GET:/users/:id': {
  path: '/users/:id',
  method: 'GET',
  handler: async (request, context) => {
    const userId = context?.params?.id;
    // Handle the request
  },
}
```

## Middleware

### Global Middleware

Add middleware that applies to all routes:

```typescript
import { defaultRouteProcessor } from '@/lib/routes/processor';

const loggingMiddleware = async (request, context, next) => {
  console.log(`Request: ${request.method} ${request.url}`);
  const response = await next();
  console.log(`Response: ${response.status}`);
  return response;
};

defaultRouteProcessor.addGlobalMiddleware(loggingMiddleware);
```

## Development Tools

### Route Inspector

Access route information in development:

```
GET /api/dev/routes?action=stats
GET /api/dev/routes?action=table
GET /api/dev/routes?action=validate
```

### Route Testing

Test route matching:

```
GET /api/dev/routes?action=test&method=GET&path=/auth/login
```

### Route Search

Find routes by pattern:

```
GET /api/dev/routes?action=search&pattern=auth
```

## Migration from Existing Routes

The system includes a compatibility layer for gradual migration:

1. Existing routes continue to work
2. New centralized handlers are used when available
3. Legacy handlers are kept for backward compatibility
4. Routes can be migrated one by one

### Migration Steps

1. Create handler in `/src/lib/routes/handlers/`
2. Add route to registry
3. Update existing route file to use compatibility layer
4. Test the route
5. Remove legacy handler when confident

## Best Practices

### Route Organization

- Group related routes by feature
- Use consistent naming conventions
- Add descriptive comments
- Include proper error handling

### Security

- Always validate input parameters
- Use rate limiting for all routes
- Require authentication where appropriate
- Sanitize response data

### Performance

- Use appropriate rate limits
- Cache responses when possible
- Minimize database queries
- Use pagination for list endpoints

### Documentation

- Add descriptions to all routes
- Use tags for grouping
- Include examples in comments
- Keep documentation up to date

## Troubleshooting

### Route Not Found

1. Check if route is registered in registry
2. Verify path pattern matches request
3. Check HTTP method matches
4. Use development tools to test matching

### Authentication Issues

1. Verify `requiresAuth` is set correctly
2. Check JWT token is valid
3. Ensure user has proper permissions
4. Check middleware order

### Rate Limiting

1. Check rate limit configuration
2. Verify key generator is working
3. Test with different IP addresses
4. Monitor rate limit logs

## Examples

See the existing handlers in `/src/lib/routes/handlers/` for complete examples of:
- Authentication handling
- Input validation
- Error responses
- Rate limiting
- Response formatting
