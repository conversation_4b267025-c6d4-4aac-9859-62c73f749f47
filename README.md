# WhiteLotus Invoice Application

A modern invoice management system built with Next.js, featuring secure authentication, invoice management, and PDF generation capabilities.

## Features

- 🔐 Secure JWT-based authentication
- 📄 Invoice management (create, view, download)
- 🛡️ Rate limiting and security headers
- 🎨 Modern UI with Tailwind CSS
- 📱 Responsive design
- 🔒 Environment-based configuration

## Getting Started

### 1. Environment Setup

**Important:** Set up environment variables before running the application.

```bash
# Copy the example environment file
cp .env.example .env.local

# Edit .env.local with your configuration
# See ENVIRONMENT_SETUP.md for detailed instructions
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Run the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

### 4. Access the Application

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

**Default Login Credentials:**
- Email: `<EMAIL>`
- Password: `Pass@1234`

*Note: These are demo credentials. In production, implement proper user authentication with a database.*

## Environment Configuration

This application uses environment variables for all configuration. See [`ENVIRONMENT_SETUP.md`](./ENVIRONMENT_SETUP.md) for detailed setup instructions.

### Quick Environment Setup

1. **Copy example file:** `cp .env.example .env.local`
2. **Generate JWT secret:** `openssl rand -base64 32`
3. **Update `.env.local`** with your values
4. **Restart development server**

### Critical Security Notes

- ⚠️ **Change the default JWT_SECRET in production**
- ⚠️ **Update admin credentials for production use**
- ⚠️ **Use HTTPS in production environments**
- ⚠️ **Never commit `.env.local` or production secrets**

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── api/            # API routes
│   ├── dashboard/      # Dashboard pages
│   ├── invoice/        # Invoice pages
│   └── login/          # Authentication pages
├── components/         # Reusable UI components
├── context/           # React context providers
├── hooks/             # Custom React hooks
├── lib/               # Utility libraries
│   ├── config.ts      # Environment-based configuration
│   ├── auth.ts        # Authentication utilities
│   └── security.ts    # Security utilities
└── types/             # TypeScript type definitions
```

## API Endpoints

### Authentication
- `POST /api/login` - User login
- `POST /api/auth/logout` - User logout

### Invoices
- `GET /api/auth/invoice` - List invoices (paginated)
- `GET /api/auth/invoice/[id]` - Get invoice by ID
- `POST /api/auth/invoice` - Create new invoice
- `PUT /api/auth/invoice/[id]` - Update invoice
- `DELETE /api/auth/invoice/[id]` - Delete invoice

### Downloads
- `GET /api/invoice/[filename]` - Download invoice PDF

## Security Features

- 🔐 JWT-based authentication
- 🛡️ Rate limiting on sensitive endpoints
- 🔒 HTTP-only cookies for token storage
- 🛡️ Security headers (CSP, XSS protection, etc.)
- 🔍 Input validation and sanitization
- 🚫 CSRF protection

## Development

### Available Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

### Environment Files

- `.env` - Default values (committed)
- `.env.local` - Local development overrides (not committed)
- `.env.example` - Template file (committed)
- `.env.production` - Production values (not committed)

## Deployment

### Environment Variables Setup

Before deploying, ensure these environment variables are set:

**Required:**
- `JWT_SECRET` - Secure random string for JWT signing
- `NEXT_PUBLIC_API_URL` - Your API base URL
- `ADMIN_EMAIL` - Admin login email
- `ADMIN_PASSWORD` - Admin login password

**Optional:**
- `API_TIMEOUT` - API request timeout (default: 10000ms)
- `RATE_LIMIT_LOGIN_ATTEMPTS` - Login rate limit (default: 5)
- `PAGINATION_DEFAULT_LIMIT` - Default page size (default: 5)

### Vercel Deployment

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Docker Deployment

```dockerfile
# Example Dockerfile configuration
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## Learn More

### Next.js Resources
- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial
- [Next.js GitHub repository](https://github.com/vercel/next.js)

### Security Resources
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Next.js Security](https://nextjs.org/docs/advanced-features/security-headers)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Update documentation
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
