# =============================================================================
# WHITELOTUS INVOICE APP - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env.local and fill in your actual values.
# This file serves as a template and should be committed to version control.
# The actual .env files should never be committed.

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
NODE_ENV=development

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base URL for API calls (public - exposed to client)
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# API timeout in milliseconds
API_TIMEOUT=10000

# Number of API retry attempts
API_RETRIES=3

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Secret Key - CHANGE THIS IN PRODUCTION!
# Generate a strong secret: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-please

# Authentication cookie name
COOKIE_NAME=auth-token

# Cookie max age in seconds (7 days = 604800)
COOKIE_MAX_AGE=604800

# =============================================================================
# HARDCODED CREDENTIALS (FOR DEMO PURPOSES ONLY)
# =============================================================================
# WARNING: These are demo credentials. In production, use a proper database
# and authentication system instead of hardcoded values.
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Pass@1234

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Default pagination limit
PAGINATION_DEFAULT_LIMIT=5

# Maximum pagination limit
PAGINATION_MAX_LIMIT=100

# ISR (Incremental Static Regeneration) revalidate time in seconds
ISR_REVALIDATE=60

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate limiting configuration
RATE_LIMIT_LOGIN_ATTEMPTS=5
RATE_LIMIT_LOGIN_WINDOW=60000

RATE_LIMIT_DOWNLOAD_ATTEMPTS=20
RATE_LIMIT_DOWNLOAD_WINDOW=60000

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable debug logging in development
DEBUG_LOGGING=true

# Development server port
PORT=3000

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# These should be overridden in production environments

# Production API URL
# NEXT_PUBLIC_API_URL=https://your-production-api.com/api

# Production JWT Secret
# JWT_SECRET=your-production-jwt-secret-here

# Production admin credentials
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=your-secure-production-password

# =============================================================================
# OPTIONAL: DATABASE CONFIGURATION
# =============================================================================
# If you plan to add a database later, configure these:

# DATABASE_URL=postgresql://username:password@localhost:5432/whitelotus_invoice
# DATABASE_SSL=false
# DATABASE_POOL_MIN=2
# DATABASE_POOL_MAX=10

# =============================================================================
# OPTIONAL: EXTERNAL SERVICES
# =============================================================================
# If you plan to integrate with external services:

# Email service configuration
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# File storage configuration
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=your-invoice-bucket

# Payment gateway configuration
# STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
# STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# If you plan to add monitoring:

# SENTRY_DSN=https://<EMAIL>/project-id
# GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
# HOTJAR_ID=your-hotjar-id
