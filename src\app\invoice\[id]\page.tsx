import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { invoiceService } from '@/lib/services/invoiceService';
import { sanitizeApiResponse } from '@/lib/security';
import InvoiceDetailsContent from './InvoiceDetailsContent';
import LoadingSpinner from '@/components/LoadingSpinner';

// This page relies on client-side authentication via AuthContext
export default async function InvoiceDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  // No server-side auth check - handled by middleware and client-side AuthContext

  // Validate ID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(params.id)) {
    notFound();
  }

  let invoice;
  try {
    const response = await invoiceService.getInvoiceById(params.id);
    if (response.status === 200 && response.data) {
      // Sanitize the invoice data to prevent XSS
      invoice = sanitizeApiResponse(response.data);
    } else {
      notFound();
    }
  } catch (error) {
    console.error('Failed to fetch invoice:', error);
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <Suspense
          fallback={
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          }
        >
          <InvoiceDetailsContent invoice={invoice} />
        </Suspense>
      </div>
    </div>
  );
}
