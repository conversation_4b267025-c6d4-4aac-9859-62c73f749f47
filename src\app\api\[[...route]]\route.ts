import { NextRequest } from 'next/server';
import { defaultRouteProcessor } from '@/lib/routes/processor';

/**
 * Unified API route handler
 * This is the single entry point for all API routes
 * It uses the centralized route system to process requests
 */

export async function GET(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function POST(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function PUT(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function PATCH(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function DELETE(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function OPTIONS(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}

export async function HEAD(request: NextRequest) {
  return await defaultRouteProcessor.processRequest(request);
}
