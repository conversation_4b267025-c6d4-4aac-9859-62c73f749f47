import { NextRequest, NextResponse } from 'next/server';
import { getSecurityHeaders, checkRateLimit } from '@/lib/security';
import { SECURITY_CONFIG } from '@/lib/config';
import { mediaHandlers } from '@/lib/routes/handlers/media';
import { createCompatibleHandler } from '@/lib/routes/compatibility';

// Use the centralized handler
export const GET = createCompatibleHandler(mediaHandlers.downloadInvoice);

// Legacy handler for backward compatibility (will be removed in future)
// GET /api/invoice/[filename] - Download invoice file (mock)
export async function GET_LEGACY(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    // Rate limiting using environment-configured values
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(
      `download:${clientIP}`,
      SECURITY_CONFIG.RATE_LIMIT.DOWNLOAD_ATTEMPTS,
      SECURITY_CONFIG.RATE_LIMIT.DOWNLOAD_WINDOW
    )) {
      return NextResponse.json(
        { status: 429, message: 'Too many requests' },
        { status: 429, headers: getSecurityHeaders() }
      );
    }

    const { filename } = await params;

    // Validate filename (security check)
    const filenameRegex = /^[a-zA-Z0-9_-]+\.pdf$/;
    if (!filenameRegex.test(filename)) {
      return NextResponse.json(
        { status: 400, message: 'Invalid filename' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Mock PDF content (in a real app, this would fetch from storage)
    const mockPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Mock Invoice PDF - ${filename}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;

    // Create response with PDF headers
    const response = new NextResponse(mockPdfContent, {
      status: 200,
      headers: {
        ...getSecurityHeaders(),
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'private, no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

    return response;
  } catch (error) {
    console.error('Download invoice error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
