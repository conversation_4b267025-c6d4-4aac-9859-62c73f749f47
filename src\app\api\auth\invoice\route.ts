import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUserFromRequest } from '@/lib/auth';
import { getSecurityHeaders, sanitizeApiResponse, checkRateLimit } from '@/lib/security';
import { mockInvoices, createMockInvoice } from '@/lib/mockData';
import { CreateInvoiceRequest } from '@/types';
import { invoiceHandlers } from '@/lib/routes/handlers/invoice';
import { createCompatibleHandler } from '@/lib/routes/compatibility';

// Use the centralized handlers
export const GET = createCompatibleHandler(invoiceHandlers.list);
export const POST = createCompatibleHandler(invoiceHandlers.create);

// Legacy handlers for backward compatibility (will be removed in future)
// GET /api/auth/invoice - List invoices with pagination
export async function GET_LEGACY(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(`invoices:${clientIP}`, 30, 60000)) {
      return NextResponse.json(
        { status: 429, message: 'Too many requests' },
        { status: 429, headers: getSecurityHeaders() }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0', 10);
    const limit = Math.min(parseInt(searchParams.get('limit') || '10', 10), 100);

    // Validate pagination parameters
    if (skip < 0 || limit < 1) {
      return NextResponse.json(
        { status: 400, message: 'Invalid pagination parameters' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Apply pagination
    const paginatedInvoices = mockInvoices.slice(skip, skip + limit);

    // Sanitize response data
    const sanitizedInvoices = sanitizeApiResponse(paginatedInvoices);

    return NextResponse.json({
      status: 200,
      message: 'Invoices fetched successfully',
      data: sanitizedInvoices,
      pagination: {
        skip,
        limit,
        total: mockInvoices.length,
      },
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('Get invoices error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}

// POST /api/auth/invoice - Create new invoice
export async function POST_LEGACY(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(`create-invoice:${clientIP}`, 10, 60000)) {
      return NextResponse.json(
        { status: 429, message: 'Too many requests' },
        { status: 429, headers: getSecurityHeaders() }
      );
    }

    const body: CreateInvoiceRequest = await request.json();
    const { client_name, amount, status, due_date, invoice_file_name } = body;

    // Validate required fields
    if (!client_name || !amount || !due_date || !invoice_file_name) {
      return NextResponse.json(
        { status: 400, message: 'Missing required fields' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Validate data types and ranges
    if (typeof amount !== 'number' || amount <= 0 || amount > 999999.99) {
      return NextResponse.json(
        { status: 400, message: 'Invalid amount' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    if (!['PENDING', 'PAID', 'OVERDUE'].includes(status)) {
      return NextResponse.json(
        { status: 400, message: 'Invalid status' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Validate due date
    const dueDateObj = new Date(due_date);
    if (isNaN(dueDateObj.getTime())) {
      return NextResponse.json(
        { status: 400, message: 'Invalid due date' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Create new invoice
    const newInvoice = createMockInvoice({
      client_name: client_name.trim(),
      amount,
      status,
      due_date,
      invoice_file_name: invoice_file_name.trim(),
      invoice_file_url: `/invoice/${invoice_file_name.trim()}`,
    });

    // Add to mock data (in a real app, this would be saved to database)
    mockInvoices.unshift(newInvoice);

    // Sanitize response data
    const sanitizedInvoice = sanitizeApiResponse(newInvoice);

    return NextResponse.json({
      status: 200,
      message: 'Invoice created successfully',
      data: sanitizedInvoice,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('Create invoice error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
