import { Invoice } from '@/types';

// Mock invoice data
export const mockInvoices: Invoice[] = [
  {
    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    invoice_number: 'INV-2024-001',
    client_name: 'Acme Corporation',
    amount: 2500.00,
    status: 'PAID',
    due_date: '2024-01-15T00:00:00.000Z',
    invoice_file_name: 'invoice_001.pdf',
    invoice_file_url: '/invoice/invoice_001.pdf',
    is_active: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-10T00:00:00.000Z',
  },
  {
    id: '4fb85f64-5717-4562-b3fc-2c963f66afa7',
    invoice_number: 'INV-2024-002',
    client_name: 'Tech Solutions Inc',
    amount: 1750.50,
    status: 'PENDING',
    due_date: '2024-02-28T00:00:00.000Z',
    invoice_file_name: 'invoice_002.pdf',
    invoice_file_url: '/invoice/invoice_002.pdf',
    is_active: true,
    createdAt: '2024-01-15T00:00:00.000Z',
    updatedAt: '2024-01-15T00:00:00.000Z',
  },
  {
    id: '5gc85f64-5717-4562-b3fc-2c963f66afa8',
    invoice_number: 'INV-2024-003',
    client_name: 'Global Enterprises',
    amount: 3200.75,
    status: 'OVERDUE',
    due_date: '2023-12-31T00:00:00.000Z',
    invoice_file_name: 'invoice_003.pdf',
    invoice_file_url: '/invoice/invoice_003.pdf',
    is_active: true,
    createdAt: '2023-12-01T00:00:00.000Z',
    updatedAt: '2023-12-01T00:00:00.000Z',
  },
  {
    id: '6hd85f64-5717-4562-b3fc-2c963f66afa9',
    invoice_number: 'INV-2024-004',
    client_name: 'StartupXYZ',
    amount: 890.25,
    status: 'PENDING',
    due_date: '2024-03-15T00:00:00.000Z',
    invoice_file_name: 'invoice_004.pdf',
    invoice_file_url: '/invoice/invoice_004.pdf',
    is_active: true,
    createdAt: '2024-02-01T00:00:00.000Z',
    updatedAt: '2024-02-01T00:00:00.000Z',
  },
  {
    id: '7ie85f64-5717-4562-b3fc-2c963f66afaa',
    invoice_number: 'INV-2024-005',
    client_name: 'Manufacturing Co',
    amount: 4500.00,
    status: 'PAID',
    due_date: '2024-02-01T00:00:00.000Z',
    invoice_file_name: 'invoice_005.pdf',
    invoice_file_url: '/invoice/invoice_005.pdf',
    is_active: true,
    createdAt: '2024-01-20T00:00:00.000Z',
    updatedAt: '2024-01-25T00:00:00.000Z',
  },
  {
    id: '8jf85f64-5717-4562-b3fc-2c963f66afab',
    invoice_number: 'INV-2024-006',
    client_name: 'Retail Chain Ltd',
    amount: 1200.00,
    status: 'PENDING',
    due_date: '2024-04-01T00:00:00.000Z',
    invoice_file_name: 'invoice_006.pdf',
    invoice_file_url: '/invoice/invoice_006.pdf',
    is_active: true,
    createdAt: '2024-02-15T00:00:00.000Z',
    updatedAt: '2024-02-15T00:00:00.000Z',
  },
  {
    id: '9kg85f64-5717-4562-b3fc-2c963f66afac',
    invoice_number: 'INV-2024-007',
    client_name: 'Consulting Group',
    amount: 2800.50,
    status: 'OVERDUE',
    due_date: '2024-01-01T00:00:00.000Z',
    invoice_file_name: 'invoice_007.pdf',
    invoice_file_url: '/invoice/invoice_007.pdf',
    is_active: true,
    createdAt: '2023-12-15T00:00:00.000Z',
    updatedAt: '2023-12-15T00:00:00.000Z',
  },
  {
    id: '0lh85f64-5717-4562-b3fc-2c963f66afad',
    invoice_number: 'INV-2024-008',
    client_name: 'Digital Agency',
    amount: 1950.75,
    status: 'PAID',
    due_date: '2024-02-10T00:00:00.000Z',
    invoice_file_name: 'invoice_008.pdf',
    invoice_file_url: '/invoice/invoice_008.pdf',
    is_active: true,
    createdAt: '2024-01-25T00:00:00.000Z',
    updatedAt: '2024-02-05T00:00:00.000Z',
  },
];

export function generateInvoiceNumber(): string {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `INV-${year}-${randomNum}`;
}

export function createMockInvoice(data: Partial<Invoice>): Invoice {
  const now = new Date().toISOString();
  return {
    id: crypto.randomUUID(),
    invoice_number: generateInvoiceNumber(),
    client_name: '',
    amount: 0,
    status: 'PENDING',
    due_date: now,
    invoice_file_name: '',
    invoice_file_url: '',
    is_active: true,
    createdAt: now,
    updatedAt: now,
    ...data,
  };
}
