import { Suspense } from 'react';
import { APP_CONFIG } from '@/lib/config';
import DashboardContent from './DashboardContent';
import LoadingSpinner from '@/components/LoadingSpinner';

// This page relies on client-side authentication via AuthContext
export default async function DashboardPage({
  searchParams,
}: {
  searchParams: { page?: string };
}) {
  // No server-side auth check - handled by middleware and client-side AuthContext

  const currentPage = parseInt(searchParams.page || '1', 10);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-900">
                Invoice Dashboard
              </h1>
              <p className="mt-2 text-sm text-gray-600">
                Welcome back, {user.first_name} {user.last_name}
              </p>
            </div>

            <Suspense
              fallback={
                <div className="flex justify-center items-center py-12">
                  <LoadingSpinner size="lg" />
                </div>
              }
            >
              <DashboardContent currentPage={currentPage} />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}

// Enable ISR with 60 seconds revalidation
export const revalidate = APP_CONFIG.ISR_REVALIDATE;
