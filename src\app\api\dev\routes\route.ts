import { NextRequest, NextResponse } from 'next/server';
import { RouteAdmin } from '@/lib/routes/admin';
import { getSecurityHeaders } from '@/lib/security';

/**
 * Development route for inspecting and testing the route system
 * Only available in development mode
 */

export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { status: 404, message: 'Not found' },
      { status: 404, headers: getSecurityHeaders() }
    );
  }

  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'stats';

  try {
    let data;

    switch (action) {
      case 'stats':
        data = RouteAdmin.getRouteStats();
        break;

      case 'table':
        data = RouteAdmin.getRouteTable();
        break;

      case 'tags':
        data = RouteAdmin.getRoutesByTags();
        break;

      case 'validate':
        data = RouteAdmin.validateRoutes();
        break;

      case 'metrics':
        data = RouteAdmin.getRouteMetrics();
        break;

      case 'openapi':
        data = RouteAdmin.generateOpenApiSpec();
        break;

      case 'search':
        const pattern = searchParams.get('pattern') || '';
        data = RouteAdmin.findRoutes(pattern);
        break;

      case 'test':
        const method = searchParams.get('method') || 'GET';
        const path = searchParams.get('path') || '/';
        data = RouteAdmin.testRouteMatching(method, path);
        break;

      default:
        return NextResponse.json(
          { 
            status: 400, 
            message: 'Invalid action',
            availableActions: ['stats', 'table', 'tags', 'validate', 'metrics', 'openapi', 'search', 'test']
          },
          { status: 400, headers: getSecurityHeaders() }
        );
    }

    return NextResponse.json({
      status: 200,
      message: `Route ${action} retrieved successfully`,
      data,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });

  } catch (error) {
    console.error('Route dev error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { status: 404, message: 'Not found' },
      { status: 404, headers: getSecurityHeaders() }
    );
  }

  try {
    const body = await request.json();
    const { action, ...params } = body;

    let result;

    switch (action) {
      case 'test-route':
        result = RouteAdmin.testRouteMatching(params.method, params.path);
        break;

      case 'search-routes':
        result = RouteAdmin.findRoutes(params.pattern);
        break;

      default:
        return NextResponse.json(
          { 
            status: 400, 
            message: 'Invalid action',
            availableActions: ['test-route', 'search-routes']
          },
          { status: 400, headers: getSecurityHeaders() }
        );
    }

    return NextResponse.json({
      status: 200,
      message: `Route ${action} completed successfully`,
      data: result,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });

  } catch (error) {
    console.error('Route dev POST error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
