import { NextRequest, NextResponse } from 'next/server';
import { createToken, createAuth<PERSON><PERSON>ie } from '@/lib/auth';
import { checkRateLimit, validateEmail, getSecurityHeaders } from '@/lib/security';
import { DEMO_CREDENTIALS, SECURITY_CONFIG } from '@/lib/config';
import { LoginRequest, User } from '@/types';
import { authHandlers } from '@/lib/routes/handlers/auth';
import { createCompatibleHandler } from '@/lib/routes/compatibility';

// Use the centralized handler
export const POST = createCompatibleHandler(authHandlers.login);

// Legacy handler for backward compatibility (will be removed in future)
export async function POST_LEGACY(request: NextRequest) {
  try {
    // Rate limiting using environment-configured values
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(
      `login:${clientIP}`,
      SECURITY_CONFIG.RATE_LIMIT.LOGIN_ATTEMPTS,
      SECURITY_CONFIG.RATE_LIMIT.LOGIN_WINDOW
    )) {
      return NextResponse.json(
        { status: 429, message: 'Too many login attempts. Please try again later.' },
        { status: 429, headers: getSecurityHeaders() }
      );
    }

    const body: LoginRequest = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { status: 400, message: 'Email and password are required' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { status: 400, message: 'Invalid email format' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Check credentials against environment-configured demo values
    if (email !== DEMO_CREDENTIALS.EMAIL || password !== DEMO_CREDENTIALS.PASSWORD) {
      return NextResponse.json(
        { status: 400, message: 'Invalid credentials' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Create user object (mock data based on API documentation)
    const user: User = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      first_name: 'Admin',
      last_name: 'Fintech',
      email: email,
      is_active: true,
      token: '', // Will be set below
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString(),
    };

    // Create JWT token
    const token = await createToken({ user });
    user.token = token;

    // Create response
    const response = NextResponse.json({
      status: 200,
      message: 'User loggedIn successfully',
      data: user,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });

    // Set HTTP-only cookie
    const cookie = createAuthCookie(token);
    response.cookies.set(cookie);

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
